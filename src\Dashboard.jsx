// Dashboard UI sesuai desain Figma
import React from "react";

const imgEllipse2 = "http://localhost:3845/assets/9237a672d17b09e8d5e4a161050c58bffbdb3953.png";
const imgEllipse3 = "http://localhost:3845/assets/42cfaa8715c3f7e74ed0692b777aceefa43dafc9.png";
const imgEllipse1 = "http://localhost:3845/assets/d9388fc6a5e78a54f146d68355d5dd7f5bce794c.svg";
const img = "http://localhost:3845/assets/b0505635bca8e1c38638c2333c4322ffa7fbc463.svg";
const imgEllipse6 = "http://localhost:3845/assets/61589ae5736335d48794f052b54d4a6391ce3bc8.svg";
const imgEllipse7 = "http://localhost:3845/assets/f6e081f23dcc27bc497afe4d268853e782c0a0bb.svg";
const imgEllipse8 = "http://localhost:3845/assets/0148b96267be3a85d016391aebf1b456fd4dba6f.svg";
const imgEllipse9 = "http://localhost:3845/assets/1a733bed599907b65d119c2095e4adcc3fa76f4e.svg";
const img1 = "http://localhost:3845/assets/bac938cb0eb1bec99896f5ab433f962559dd9f6a.svg";
const img2 = "http://localhost:3845/assets/0a88539bc05a8beca4151da16462f6e15373ea9c.svg";
const img3 = "http://localhost:3845/assets/9c97159fb005baa8c889b03f1eda2146ff85c026.svg";
const img4 = "http://localhost:3845/assets/2fb4e5175ca1628d57499ab87c071a1b97f8242f.svg";
const img5 = "http://localhost:3845/assets/ea8cdf5a707360d9ef3f2bd6aeebf9fd97e93939.svg";
const img6 = "http://localhost:3845/assets/a643c6cc632b3787ba1d4ae73c68bbc9da00c807.svg";
const img7 = "http://localhost:3845/assets/53b1102c2255b09175ee3813973e7544ba88e3a4.svg";
const img8 = "http://localhost:3845/assets/3bb2ab41a5dc898a2d66ea781e17e31c62ddd729.svg";
const imgEllipse4 = "http://localhost:3845/assets/2592ab2698c8e348f6c0716052d772e6b81cbe83.svg";
const imgEllipse5 = "http://localhost:3845/assets/d0564a311fc863b6f40cccf336cd32f7c393a4cc.svg";
const imgEllipse10 = "http://localhost:3845/assets/20cbfad1d3cf7cbbb97a01962c53ccb28b45c25a.svg";

export default function Dashboard() {
  return (
    <div className="box-border flex flex-col gap-6 p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex flex-row justify-between items-center mb-6">
        <div className="flex flex-row items-end gap-3">
          <div className="relative w-[69px] h-[69px]">
            <img src={imgEllipse1} alt="Ellipse1" className="absolute w-full h-full" />
            <img src={imgEllipse2} alt="Ellipse2" className="absolute w-full h-full" />
          </div>
          <div>
            <h1 className="font-semibold text-2xl text-black">Lorem ipsum dolor sit amet</h1>
            <p className="text-xs text-gray-500">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. Aliquam in hendrerit urna.</p>
          </div>
        </div>
        <button className="flex items-center gap-2 px-4 py-2 border border-gray-400 rounded-lg">
          <img src={img} alt="Logout" className="w-4 h-4" />
          <span className="text-gray-700">Log Out</span>
        </button>
      </div>
      {/* Main Content */}
      <div className="flex flex-row gap-4">
        {/* Left Container */}
        <div className="flex-1 flex flex-col gap-6">
          {/* Statistic Cards */}
          <div className="flex flex-row gap-3">
            <div className="bg-white rounded-2xl w-[202px] h-[72px] flex items-center px-4 py-3 shadow">
              <div className="flex-1">
                <div className="font-semibold text-2xl text-gray-900">3</div>
                <div className="text-[10px] text-gray-700">Total Analysis</div>
              </div>
              <img src={imgEllipse6} alt="Ellipse6" className="w-[52px] h-[52px]" />
            </div>
            <div className="bg-white rounded-2xl w-[202px] h-[72px] flex items-center px-4 py-3 shadow">
              <div className="flex-1">
                <div className="font-semibold text-2xl text-gray-900">4</div>
                <div className="text-[10px] text-gray-700">Completed</div>
              </div>
              <img src={imgEllipse7} alt="Ellipse7" className="w-[52px] h-[52px]" />
            </div>
            <div className="bg-white rounded-2xl w-[202px] h-[72px] flex items-center px-4 py-3 shadow">
              <div className="flex-1">
                <div className="font-semibold text-2xl text-gray-900">6</div>
                <div className="text-[10px] text-gray-700">Processing</div>
              </div>
              <img src={imgEllipse8} alt="Ellipse8" className="w-[52px] h-[52px]" />
            </div>
            <div className="bg-white rounded-2xl w-[202px] h-[72px] flex items-center px-4 py-3 shadow">
              <div className="flex-1">
                <div className="font-semibold text-2xl text-gray-900">2</div>
                <div className="text-[10px] text-gray-700">Token Balance</div>
              </div>
              <img src={imgEllipse9} alt="Ellipse9" className="w-[52px] h-[52px]" />
            </div>
          </div>
          {/* Assessment Table */}
          <div className="bg-white rounded-xl shadow p-4">
            <div className="flex justify-between items-center mb-2">
              <div>
                <div className="font-semibold text-lg text-black">Assessment History</div>
                <div className="text-xs text-gray-500">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut et massa mi. Aliquam in hendrerit urna.</div>
              </div>
              <button className="flex items-center gap-2 bg-primary-600 text-white px-3 py-2 rounded-lg">
                <img src={img1} alt="Add" className="w-4 h-4" />
                <span className="font-bold text-xs">New Assessment</span>
              </button>
            </div>
            <table className="w-full text-left text-sm">
              <thead>
                <tr className="border-b">
                  <th className="py-2">Nomor</th>
                  <th className="py-2">Nama</th>
                  <th className="py-2">Tipe Ujian</th>
                  <th className="py-2">Tanggal Ujian</th>
                  <th className="py-2">Action</th>
                </tr>
              </thead>
              <tbody>
                {/* Data rows, contoh statis */}
                <tr className="border-b">
                  <td className="py-2">1</td>
                  <td className="py-2">Matematika</td>
                  <td className="py-2">PG</td>
                  <td className="py-2">12 Juni 2024</td>
                  <td className="py-2 flex gap-2">
                    <img src={img3} alt="View" className="w-6 h-6" />
                    <img src={img7} alt="Delete" className="w-6 h-6" />
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="py-2">2</td>
                  <td className="py-2">Bahasa Inggris</td>
                  <td className="py-2">PG</td>
                  <td className="py-2">12 Juni 2024</td>
                  <td className="py-2 flex gap-2">
                    <img src={img3} alt="View" className="w-6 h-6" />
                    <img src={img7} alt="Delete" className="w-6 h-6" />
                  </td>
                </tr>
                <tr>
                  <td className="py-2">3</td>
                  <td className="py-2">Bahasa Indonesia</td>
                  <td className="py-2">PG</td>
                  <td className="py-2">22 Juni 2024</td>
                  <td className="py-2 flex gap-2">
                    <img src={img3} alt="View" className="w-6 h-6" />
                    <img src={img7} alt="Delete" className="w-6 h-6" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        {/* Right Container */}
        <div className="w-[396px] flex flex-col gap-4">
          {/* Profile Card */}
          <div className="bg-white rounded-xl shadow p-4 flex flex-col items-center">
            <div className="relative w-40 h-40 mb-4">
              <img src={imgEllipse4} alt="Ellipse4" className="absolute w-full h-full" />
              <img src={imgEllipse5} alt="Ellipse5" className="absolute w-full h-full" />
              <img src={imgEllipse3} alt="Ellipse3" className="absolute w-[126px] h-[126px] right-4 bottom-4" />
            </div>
            <div className="text-center">
              <div className="font-semibold text-xl text-black">Lorem ipsum dolor sit amet</div>
              <div className="text-xs text-gray-500">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
            </div>
          </div>
          {/* Bar Chart */}
          <div className="bg-white rounded-2xl shadow p-4 flex flex-col items-center">
            <div className="flex gap-2 mb-2">
              <div className="w-[59px] h-[136px] bg-gray-200 rounded-xl relative">
                <div className="absolute bottom-0 left-0 w-full h-[102px] bg-primary-400 rounded-xl" />
              </div>
              <div className="w-[59px] h-[136px] bg-gray-200 rounded-xl relative">
                <div className="absolute bottom-0 left-0 w-full h-[122px] bg-primary-600 rounded-xl" />
              </div>
              <div className="w-[59px] h-[136px] bg-gray-200 rounded-xl relative">
                <div className="absolute bottom-0 left-0 w-full h-[37px] bg-primary-400 rounded-xl" />
              </div>
              <div className="w-[59px] h-[136px] bg-gray-200 rounded-xl relative">
                <div className="absolute bottom-0 left-0 w-full h-[78px] bg-primary-600 rounded-xl" />
              </div>
              <div className="w-[59px] h-[136px] bg-gray-200 rounded-xl relative">
                <div className="absolute bottom-0 left-0 w-full h-10 bg-primary-400 rounded-xl" />
              </div>
            </div>
            <div className="flex flex-row justify-between w-full px-6">
              {[...Array(5)].map((_, i) => (
                <img key={i} src={imgEllipse10} alt="dot" className="w-6 h-6" />
              ))}
            </div>
          </div>
          {/* Skill List */}
          <div className="bg-white rounded-xl shadow p-4">
            <div className="mb-2">
              <div className="font-semibold text-lg text-black">Lorem ipsum dolor sit amet</div>
              <div className="text-xs text-gray-500">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
            </div>
            <div className="flex flex-col gap-4">
              {/* Skill Bar Example */}
              <div className="flex items-center gap-3">
                <span className="text-xs font-medium text-black">Lorem ipsum</span>
                <div className="flex-1 h-2.5 bg-gray-200 rounded-full relative">
                  <div className="absolute left-0 top-0 h-2.5 bg-primary-600 rounded-full" style={{ width: '71%' }} />
                </div>
                <span className="text-[10px] font-semibold text-black">71%</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-xs font-medium text-black">Lorem ipsum</span>
                <div className="flex-1 h-2.5 bg-gray-200 rounded-full relative">
                  <div className="absolute left-0 top-0 h-2.5 bg-primary-600 rounded-full" style={{ width: '92%' }} />
                </div>
                <span className="text-[10px] font-semibold text-black">92%</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-xs font-medium text-black">Lorem ipsum</span>
                <div className="flex-1 h-2.5 bg-gray-200 rounded-full relative">
                  <div className="absolute left-0 top-0 h-2.5 bg-primary-600 rounded-full" style={{ width: '32%' }} />
                </div>
                <span className="text-[10px] font-semibold text-black">32%</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-xs font-medium text-black">Lorem ipsum</span>
                <div className="flex-1 h-2.5 bg-gray-200 rounded-full relative">
                  <div className="absolute left-0 top-0 h-2.5 bg-primary-600 rounded-full" style={{ width: '54%' }} />
                </div>
                <span className="text-[10px] font-semibold text-black">54%</span>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-xs font-medium text-black">Lorem ipsum</span>
                <div className="flex-1 h-2.5 bg-gray-200 rounded-full relative">
                  <div className="absolute left-0 top-0 h-2.5 bg-primary-600 rounded-full" style={{ width: '86%' }} />
                </div>
                <span className="text-[10px] font-semibold text-black">86%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
